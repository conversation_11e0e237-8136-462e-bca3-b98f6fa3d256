<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>COMMIT_EDITMSG</string>
		<string>MERGE_MSG</string>
	</array>
	<key>foldingStartMarker</key>
	<string>^\+\+\+</string>
	<key>foldingStopMarker</key>
	<string>^---</string>
	<key>name</key>
	<string>Git Commit Message</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>begin</key>
			<string>\A(?!# Please enter the commit message)</string>
			<key>end</key>
			<string>^(?=# Please enter the commit message)</string>
			<key>name</key>
			<string>meta.scope.message.git-commit</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>\A(?=#)</string>
					<key>end</key>
					<string>^(?!#)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#comment</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>^(?!# Please enter the commit message)</string>
					<key>end</key>
					<string>^(?=# Please enter the commit message)</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>\G</string>
							<key>end</key>
							<string>^(?!\G)</string>
							<key>name</key>
							<string>meta.scope.subject.git-commit</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>captures</key>
									<dict>
										<key>1</key>
										<dict>
											<key>name</key>
											<string>keyword.other.$2.git-commit</string>
										</dict>
									</dict>
									<key>match</key>
									<string>\G((fixup|squash)!)\s*</string>
								</dict>
								<dict>
									<key>match</key>
									<string>.{66,}$</string>
									<key>name</key>
									<string>invalid.illegal.line-too-long.git-commit</string>
								</dict>
								<dict>
									<key>match</key>
									<string>.{51,}$</string>
									<key>name</key>
									<string>invalid.deprecated.line-too-long.git-commit</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>begin</key>
							<string>^(?!# Please enter the commit message)</string>
							<key>end</key>
							<string>^(?=# Please enter the commit message)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#comment</string>
								</dict>
							</array>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>^(?=# Please enter the commit message)</string>
			<key>end</key>
			<string>\z</string>
			<key>name</key>
			<string>meta.scope.metadata.git-commit</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#metadata</string>
				</dict>
			</array>
		</dict>
	</array>
	<key>repository</key>
	<dict>
		<key>comment</key>
		<dict>
			<key>begin</key>
			<string>^(#)</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.comment.git-commit</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\n</string>
			<key>name</key>
			<string>comment.line.number-sign.git-commit</string>
		</dict>
		<key>metadata</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?=^# Changes to be committed:)</string>
					<key>end</key>
					<string>(?!\G)((?=^# \w)|(?!^#))</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>(^[ \t]+)?(?=#)</string>
							<key>beginCaptures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>punctuation.whitespace.comment.leading.git-commit</string>
								</dict>
							</dict>
							<key>contentName</key>
							<string>comment.line.number-sign.git-commit</string>
							<key>end</key>
							<string>(?!\G)^</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>match</key>
									<string>\G#</string>
									<key>name</key>
									<string>punctuation.definition.comment.git-commit</string>
								</dict>
								<dict>
									<key>match</key>
									<string>((modified|renamed):.*)$\n?</string>
									<key>name</key>
									<string>markup.changed.git-commit</string>
								</dict>
								<dict>
									<key>match</key>
									<string>(new file:.*)$\n?</string>
									<key>name</key>
									<string>markup.inserted.git-commit</string>
								</dict>
								<dict>
									<key>match</key>
									<string>(deleted:.*)$\n?</string>
									<key>name</key>
									<string>markup.deleted.git-commit</string>
								</dict>
							</array>
						</dict>
					</array>
				</dict>
				<dict>
					<key>include</key>
					<string>#comment</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>(?=diff\ \-\-git)</string>
					<key>comment</key>
					<string>diff presented at the end of the commit message when using commit -v.</string>
					<key>contentName</key>
					<string>source.diff</string>
					<key>end</key>
					<string>\z</string>
					<key>name</key>
					<string>meta.embedded.diff.git-commit</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.diff</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
	</dict>
	<key>scopeName</key>
	<string>text.git-commit</string>
	<key>uuid</key>
	<string>BFE83C06-8508-44BE-A975-95A57BF619A7</string>
</dict>
</plist>
