<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>ps1</string>
		<string>psm1</string>
		<string>psd1</string>
	</array>
	<key>name</key>
	<string>PowerShell</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>begin</key>
			<string>&lt;#</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.start.definition.comment.block.powershell</string>
				</dict>
			</dict>
			<key>end</key>
			<string>#&gt;</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.end.definition.comment.block.powershell</string>
				</dict>
			</dict>
			<key>name</key>
			<string>comment.block.powershell</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#commentEmbeddedDocs</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?&lt;![\\-])#</string>
			<key>end</key>
			<string>$</string>
			<key>name</key>
			<string>comment.line.number-sign.powershell</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#commentEmbeddedDocs</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>match</key>
			<string>[2-6]&gt;&amp;1|&gt;&gt;|&gt;|&lt;&lt;|&lt;|&gt;|&gt;\||[1-6]&gt;|[1-6]&gt;&gt;</string>
			<key>name</key>
			<string>keyword.operator.redirection.powershell</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#commands</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#variable</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#interpolatedStringContent</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#function</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#attribute</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#type</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?&lt;!(?&lt;!`)")"</string>
			<key>end</key>
			<string>"(?!")</string>
			<key>name</key>
			<string>string.quoted.double.powershell</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#variableNoProperty</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#doubleQuotedStringEscapes</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#interpolation</string>
				</dict>
				<dict>
					<key>match</key>
					<string>`\s*$</string>
					<key>name</key>
					<string>keyword.other.powershell</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>comment</key>
			<string>Needed to parse stuff correctly in 'argument mode'. (See about_parsing.)</string>
			<key>include</key>
			<string>#doubleQuotedStringEscapes</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?&lt;!')'</string>
			<key>end</key>
			<string>'(?!')</string>
			<key>name</key>
			<string>string.quoted.single.powershell</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>''</string>
					<key>name</key>
					<string>constant.character.escape.powershell</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\@"(?=$)</string>
			<key>end</key>
			<string>^"@</string>
			<key>name</key>
			<string>string.quoted.double.heredoc.powershell</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#variableNoProperty</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#doubleQuotedStringEscapes</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#interpolation</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\@'(?=$)</string>
			<key>end</key>
			<string>^'@</string>
			<key>name</key>
			<string>string.quoted.single.heredoc.powershell</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>''</string>
					<key>name</key>
					<string>constant.character.escape.powershell</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>include</key>
			<string>#numericConstant</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>@\(</string>
			<key>captures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>keyword.other.powershell</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\)</string>
			<key>name</key>
			<string>meta.group.array-expression.powershell</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>\$\(</string>
			<key>captures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>keyword.other.powershell</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>TODO: move to repo; make recursive.</string>
			<key>end</key>
			<string>\)</string>
			<key>name</key>
			<string>meta.group.complex.subexpression.powershell</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>match</key>
			<string>(?&lt;!\w)-([ci]?[lg][te]|eq|ne)</string>
			<key>name</key>
			<string>keyword.operator.logical.powershell</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:[a-z][a-z0-9]+-?[a-z][a-z0-9]+)(?i:\.(?i:exe|cmd|bat|ps1))</string>
			<key>name</key>
			<string>support.function.powershell</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?&lt;!\w)((?i:begin|break|catch|continue|data|define|do|dynamicparam|else|elseif|end|exit|finally|for|foreach(?!-object)|from|if|in|inlinescript|parallel|param|process|return|switch|throw|trap|try|until|using|var|where(?!=-object)|while)|%|\?)(?!\w)</string>
			<key>name</key>
			<string>keyword.control.powershell</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.powershell</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>entity.name.function</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>capture should be entity.name.type, but it doesn't provide a good color in the default schema.</string>
			<key>match</key>
			<string>(?&lt;!\w)((?i:class)|%|\?)(?:\s)+((?:\p{L}|\d|_|-|)+)\b</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?&lt;!\w)-(?i:is(?:not)?|as)\b</string>
			<key>name</key>
			<string>keyword.operator.comparison.powershell</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?&lt;!\w)-(?i:[ic]?(?:eq|ne|[gl][te]|(?:not)?(?:like|match|contains|in)|replace))(?!\p{L})</string>
			<key>name</key>
			<string>keyword.operator.comparison.powershell</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?&lt;!\w)-(?i:join|split)(?!\p{L})|!</string>
			<key>name</key>
			<string>keyword.operator.unary.powershell</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?&lt;!\w)-(?i:and|or|not|xor)(?!\p{L})|!</string>
			<key>name</key>
			<string>keyword.operator.logical.powershell</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?&lt;!\w)-(?i:band|bor|bnot|bxor)(?!\p{L})</string>
			<key>name</key>
			<string>keyword.operator.bitwise.powershell</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?&lt;!\w)-(?i:f)(?!\p{L})</string>
			<key>name</key>
			<string>keyword.operator.string-format.powershell</string>
		</dict>
		<dict>
			<key>match</key>
			<string>[+%*/-]?=|[+/*%-]</string>
			<key>name</key>
			<string>keyword.operator.assignment.powershell</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\|{2}|&amp;{2}|;</string>
			<key>name</key>
			<string>keyword.other.statement-separator.powershell</string>
		</dict>
		<dict>
			<key>match</key>
			<string>&amp;|(?&lt;!\w)\.(?= )|`|,|\|</string>
			<key>name</key>
			<string>keyword.operator.other.powershell</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>This is very imprecise, is there a syntax for 'must come after...' </string>
			<key>match</key>
			<string>(?&lt;!\s|^)\.\.(?=\d|\(|\$)</string>
			<key>name</key>
			<string>keyword.operator.range.powershell</string>
		</dict>
	</array>
	<key>repository</key>
	<dict>
		<key>attribute</key>
		<dict>
			<key>begin</key>
			<string>\[(\p{L}|\.|``\d+)+(?=\()</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>entity.name.tag</string>
				</dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>entity.name.tag</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\]</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>entity.name.tag</string>
				</dict>
			</dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>\(</string>
					<key>end</key>
					<string>\)</string>
					<key>name</key>
					<string>entity.other.attribute-name</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>captures</key>
							<dict>
								<key>0</key>
								<dict>
									<key>name</key>
									<string>entity.other.attribute.parameter.powershell</string>
								</dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>constant.language.powershell</string>
								</dict>
								<key>2</key>
								<dict>
									<key>name</key>
									<string>variable.other.powershell</string>
								</dict>
							</dict>
							<key>comment</key>
							<string>really we should match the known attributes first</string>
							<key>match</key>
							<string>(\w+)\s*=?([^"']*?|'[^']*?'|"[^"]*?")?(?=,|\))</string>
							<key>name</key>
							<string>entity.other.attribute-name.powershell</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#variable</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>commands</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>comment</key>
					<string>Verb-Noun pattern:</string>
					<key>match</key>
					<string>(?:(\p{L}|\d|_|-|\\|\:)*\\)?\b(?i:Add|Approve|Assert|Backup|Block|Checkpoint|Clear|Close|Compare|Complete|Compress|Confirm|Connect|Convert|ConvertFrom|ConvertTo|Copy|Debug|Deny|Disable|Disconnect|Dismount|Edit|Enable|Enter|Exit|Expand|Export|Find|Format|Get|Grant|Group|Hide|Import|Initialize|Install|Invoke|Join|Limit|Lock|Measure|Merge|Mount|Move|New|Open|Optimize|Out|Ping|Pop|Protect|Publish|Push|Read|Receive|Redo|Register|Remove|Rename|Repair|Request|Reset|Resize|Resolve|Restart|Restore|Resume|Revoke|Save|Search|Select|Send|Set|Show|Skip|Split|Start|Step|Stop|Submit|Suspend|Switch|Sync|Test|Trace|Unblock|Undo|Uninstall|Unlock|Unprotect|Unpublish|Unregister|Update|Use|Wait|Watch|Write)\-.+?(?:\.(?i:exe|cmd|bat|ps1))?\b</string>
					<key>name</key>
					<string>support.function.powershell</string>
				</dict>
				<dict>
					<key>comment</key>
					<string>Builtin cmdlets with reserved verbs</string>
					<key>match</key>
					<string>(?&lt;!\w)(?i:foreach-object)(?!\w)</string>
					<key>name</key>
					<string>support.function.powershell</string>
				</dict>
			</array>
		</dict>
		<key>commentEmbeddedDocs</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>constant.string.documentation.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.documentation.powershell</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?i:\s*(\.)(SYNOPSIS|DESCRIPTION|EXAMPLE|INPUTS|OUTPUTS|NOTES|LINK|COMPONENT|FUNCTIONALITY))</string>
					<key>name</key>
					<string>comment.documentation.embedded.powershell</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>constant.string.documentation.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.documentation.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.documentation.powershell</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?i:\s*(\.)(PARAMETER|FORWARDHELPTARGETNAME|FORWARDHELPCATEGORY|REMOTEHELPRUNSPACE|EXTERNALHELP)\s+([a-z0-9-_]+))</string>
					<key>name</key>
					<string>comment.documentation.embedded.powershell</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>constant.string.documentation.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.documentation.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>string.quoted.double.heredoc.powershell</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?i:requires\s+-(Version\s+\d(.\d+)?|Assembly\s+(.*)|Module\s+(.*)|PsSnapIn\s+(.*)|ShellId\s+(.*)))</string>
					<key>name</key>
					<string>comment.documentation.embedded.powershell</string>
				</dict>
			</array>
		</dict>
		<key>doubleQuotedStringEscapes</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>`[0abnfrvt"'$`]</string>
					<key>name</key>
					<string>constant.character.escape.powershell</string>
				</dict>
				<dict>
					<key>match</key>
					<string>""</string>
					<key>name</key>
					<string>constant.character.escape.powershell</string>
				</dict>
			</array>
		</dict>
		<key>function</key>
		<dict>
			<key>begin</key>
			<string>(?&lt;!\S)(?i)(function|filter|configuration|workflow)\s+(?:(global|local|script|private):)?((?:\p{L}|\d|_|-|\.)+)</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>meta.function</string>
				</dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>storage.modifier.scope.powershell</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>entity.name.function.powershell</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\{|\(</string>
		</dict>
		<key>interpolatedStringContent</key>
		<dict>
			<key>begin</key>
			<string>\(</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>keyword.other.powershell</string>
				</dict>
			</dict>
			<key>contentName</key>
			<string>interpolated.simple.source.powershell</string>
			<key>end</key>
			<string>\)</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>keyword.other.powershell</string>
				</dict>
			</dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#interpolation</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#interpolatedStringContent</string>
				</dict>
			</array>
		</dict>
		<key>interpolation</key>
		<dict>
			<key>begin</key>
			<string>(\$)\(</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>keyword.other.powershell</string>
				</dict>
			</dict>
			<key>contentName</key>
			<string>interpolated.complex.source.powershell</string>
			<key>end</key>
			<string>\)</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>keyword.other.powershell</string>
				</dict>
			</dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#interpolation</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#interpolatedStringContent</string>
				</dict>
			</array>
		</dict>
		<key>numericConstant</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.math.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>support.constant.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?&lt;!\w)(?i:(0x)([a-f0-9]+)((?i:L)?(?i:[kmgtp]b)?))(?!\w)</string>
					<key>name</key>
					<string>constant.numeric.hexadecimal.powershell</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>support.constant.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.math.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>support.constant.powershell</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?&lt;!\w)(?i:(\d*\.?\d+)(?:((?i:E)[+-]?)(\d+))?((?i:[DL])?)((?i:[kmgtp]b)?))(?!\w)</string>
					<key>name</key>
					<string>constant.numeric.scientific.powershell</string>
				</dict>
			</array>
		</dict>
		<key>scriptblock</key>
		<dict>
			<key>begin</key>
			<string>\{</string>
			<key>end</key>
			<string>\}</string>
			<key>name</key>
			<string>meta.scriptblock.powershell</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
			</array>
		</dict>
		<key>type</key>
		<dict>
			<key>begin</key>
			<string>\[</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>entity.other.attribute-name</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>name should be entity.name.type but default schema doesn't have a good color for it</string>
			<key>end</key>
			<string>\]</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>entity.other.attribute-name</string>
				</dict>
			</dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>(\p{L}|\.|``\d+)+?</string>
					<key>name</key>
					<string>entity.other.attribute-name</string>
				</dict>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
			</array>
		</dict>
		<key>variable</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>constant.language.powershell</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>These are special constants.</string>
					<key>match</key>
					<string>(\$)(?i:(False|Null|True))\b</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>support.constant.variable.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.invocation.powershell</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>These are the other built-in constants.</string>
					<key>match</key>
					<string>(\$)(?i:(Error|ExecutionContext|Host|Home|PID|PsHome|PsVersionTable|ShellID))((?:\.(?:\p{L}|\d|_)+)*\b)?\b</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>support.constant.automatic.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.invocation.powershell</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>Automatic variables are not constants, but they are read-only. In monokai (default) color schema support.variable doesn't have color, so we use constant.</string>
					<key>match</key>
					<string>(\$)(?i:(\$|\^|\?|_|Args|ConsoleFileName|Event|EventArgs|EventSubscriber|ForEach|Input|LastExitCode|Matches|MyInvocation|NestedPromptLevel|Profile|PSBoundParameters|PsCmdlet|PsCulture|PSDebugContext|PSItem|PSCommandPath|PSScriptRoot|PsUICulture|Pwd|Sender|SourceArgs|SourceEventArgs|StackTrace|Switch|This))((?:\.(?:\p{L}|\d|_)+)*\b)?\b</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>variable.language.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.invocation.powershell</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>Style preference variables as language variables so that they stand out.</string>
					<key>match</key>
					<string>(\$)(?i:(ConfirmPreference|DebugPreference|ErrorActionPreference|ErrorView|FormatEnumerationLimit|MaximumAliasCount|MaximumDriveCount|MaximumErrorCount|MaximumFunctionCount|MaximumHistoryCount|MaximumVariableCount|OFS|OutputEncoding|ProgressPreference|PsCulture|PSDebugContext|PSDefaultParameterValues|PSEmailServer|PSItem|PSModuleAutoloadingPreference|PSSenderInfo|PSSessionApplicationName|PSSessionConfigurationName|PSSessionOption|VerbosePreference|WarningPreference|WhatIfPreference))((?:\.(?:\p{L}|\d|_)+)*\b)?\b</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>storage.modifier.scope.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>variable.other.normal.powershell</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.invocation.powershell</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?i:(\$)(global|local|private|script|using|workflow):((?:\p{L}|\d|_)+))((?:\.(?:\p{L}|\d|_)+)*\b)?</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>storage.modifier.scope.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>variable.other.readwrite.powershell</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.invocation.powershell</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?i:(\$\{)(global|local|private|script|using|workflow):([^}]*[^}`])(\}))((?:\.(?:\p{L}|\d|_)+)*\b)?</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>support.variable.drive.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>variable.other.readwrite.powershell</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.invocation.powershell</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?i:(\$)((?:\p{L}|\d|_)+:)?((?:\p{L}|\d|_)+))((?:\.(?:\p{L}|\d|_)+)*\b)?</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>support.variable.drive.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>variable.other.readwrite.powershell</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.invocation.powershell</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?i:(\$\{)((?:\p{L}|\d|_)+:)?([^}]*[^}`])(\}))((?:\.(?:\p{L}|\d|_)+)*\b)?</string>
				</dict>
			</array>
		</dict>
		<key>variableNoProperty</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>constant.language.powershell</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>These are special constants.</string>
					<key>match</key>
					<string>(\$)(?i:(False|Null|True))\b</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>support.constant.variable.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.invocation.powershell</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>These are the other built-in constants.</string>
					<key>match</key>
					<string>(\$)(?i:(Error|ExecutionContext|Host|Home|PID|PsHome|PsVersionTable|ShellID))\b</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>support.variable.automatic.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.invocation.powershell</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>Automatic variables are not constants, but they are read-only...</string>
					<key>match</key>
					<string>(\$)(?i:(\$|\^|\?|_|Args|ConsoleFileName|Event|EventArgs|EventSubscriber|ForEach|Input|LastExitCode|Matches|MyInvocation|NestedPromptLevel|Profile|PSBoundParameters|PsCmdlet|PsCulture|PSDebugContext|PSItem|PSCommandPath|PSScriptRoot|PsUICulture|Pwd|Sender|SourceArgs|SourceEventArgs|StackTrace|Switch|This))\b</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>variable.language.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.invocation.powershell</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>Style preference variables as language variables so that they stand out.</string>
					<key>match</key>
					<string>(\$)(?i:(ConfirmPreference|DebugPreference|ErrorActionPreference|ErrorView|FormatEnumerationLimit|MaximumAliasCount|MaximumDriveCount|MaximumErrorCount|MaximumFunctionCount|MaximumHistoryCount|MaximumVariableCount|OFS|OutputEncoding|ProgressPreference|PsCulture|PSDebugContext|PSDefaultParameterValues|PSEmailServer|PSItem|PSModuleAutoloadingPreference|PSSenderInfo|PSSessionApplicationName|PSSessionConfigurationName|PSSessionOption|VerbosePreference|WarningPreference|WhatIfPreference))\b</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>storage.modifier.scope.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>variable.other.normal.powershell</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.invocation.powershell</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?i:(\$)(global|local|private|script|using|workflow):((?:\p{L}|\d|_)+))</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>storage.modifier.scope.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>variable.other.readwrite.powershell</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.invocation.powershell</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?i:(\$\{)(global|local|private|script|using|workflow):([^}]*[^}`])(\}))</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>support.variable.drive.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>variable.other.readwrite.powershell</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.invocation.powershell</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?i:(\$)((?:\p{L}|\d|_)+:)?((?:\p{L}|\d|_)+))</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>support.variable.drive.powershell</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>variable.other.readwrite.powershell</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>keyword.other.powershell</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.invocation.powershell</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?i:(\$\{)((?:\p{L}|\d|_)+:)?([^}]*[^}`])(\}))</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>scopeName</key>
	<string>source.powershell</string>
	<key>uuid</key>
	<string>f8f5ffb0-503e-11df-9879-0800200c9a66</string>
</dict>
</plist>