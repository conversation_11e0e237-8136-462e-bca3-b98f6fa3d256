/* tslint:disable */
/* eslint-disable */
/**
 * Continue Hub IDE API
 * API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains.
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from "../runtime";
/**
 *
 * @export
 * @interface ListAssistants200ResponseInnerConfigResult
 */
export interface ListAssistants200ResponseInnerConfigResult {
  /**
   * The unrolled assistant configuration
   * @type {object}
   * @memberof ListAssistants200ResponseInnerConfigResult
   */
  config: object | null;
  /**
   * Whether the configuration loading was interrupted
   * @type {boolean}
   * @memberof ListAssistants200ResponseInnerConfigResult
   */
  configLoadInterrupted: boolean;
  /**
   * Any errors that occurred during configuration loading
   * @type {Array<string>}
   * @memberof ListAssistants200ResponseInnerConfigResult
   */
  errors?: Array<string> | null;
}

/**
 * Check if a given object implements the ListAssistants200ResponseInnerConfigResult interface.
 */
export function instanceOfListAssistants200ResponseInnerConfigResult(
  value: object,
): value is ListAssistants200ResponseInnerConfigResult {
  if (!("config" in value) || value["config"] === undefined) return false;
  if (
    !("configLoadInterrupted" in value) ||
    value["configLoadInterrupted"] === undefined
  )
    return false;
  return true;
}

export function ListAssistants200ResponseInnerConfigResultFromJSON(
  json: any,
): ListAssistants200ResponseInnerConfigResult {
  return ListAssistants200ResponseInnerConfigResultFromJSONTyped(json, false);
}

export function ListAssistants200ResponseInnerConfigResultFromJSONTyped(
  json: any,
  ignoreDiscriminator: boolean,
): ListAssistants200ResponseInnerConfigResult {
  if (json == null) {
    return json;
  }
  return {
    config: json["config"],
    configLoadInterrupted: json["configLoadInterrupted"],
    errors: json["errors"] == null ? undefined : json["errors"],
  };
}

export function ListAssistants200ResponseInnerConfigResultToJSON(
  json: any,
): ListAssistants200ResponseInnerConfigResult {
  return ListAssistants200ResponseInnerConfigResultToJSONTyped(json, false);
}

export function ListAssistants200ResponseInnerConfigResultToJSONTyped(
  value?: ListAssistants200ResponseInnerConfigResult | null,
  ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    config: value["config"],
    configLoadInterrupted: value["configLoadInterrupted"],
    errors: value["errors"],
  };
}
