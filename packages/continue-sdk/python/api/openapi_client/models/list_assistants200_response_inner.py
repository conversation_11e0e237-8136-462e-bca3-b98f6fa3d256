# coding: utf-8

"""
    Continue Hub IDE API

    API for Continue IDE to fetch assistants and other related information. These endpoints are primarily used by the Continue IDE extensions for VS Code and JetBrains. 

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from openapi_client.models.list_assistants200_response_inner_config_result import ListAssistants200ResponseInnerConfigResult
from typing import Optional, Set
from typing_extensions import Self

class ListAssistants200ResponseInner(BaseModel):
    """
    ListAssistants200ResponseInner
    """ # noqa: E501
    config_result: ListAssistants200ResponseInnerConfigResult = Field(alias="configResult")
    owner_slug: StrictStr = Field(description="Slug of the user or organization that owns the assistant", alias="ownerSlug")
    package_slug: StrictStr = Field(description="Slug of the assistant package", alias="packageSlug")
    icon_url: Optional[StrictStr] = Field(default=None, description="Pre-signed URL for the assistant's icon", alias="iconUrl")
    on_prem_proxy_url: Optional[StrictStr] = Field(default=None, description="URL of the on-premises proxy if the organization uses one", alias="onPremProxyUrl")
    use_on_prem_proxy: Optional[StrictBool] = Field(default=None, description="Whether the organization uses an on-premises proxy", alias="useOnPremProxy")
    raw_yaml: Optional[StrictStr] = Field(default=None, description="Raw YAML configuration of the assistant", alias="rawYaml")
    __properties: ClassVar[List[str]] = ["configResult", "ownerSlug", "packageSlug", "iconUrl", "onPremProxyUrl", "useOnPremProxy", "rawYaml"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ListAssistants200ResponseInner from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of config_result
        if self.config_result:
            _dict['configResult'] = self.config_result.to_dict()
        # set to None if icon_url (nullable) is None
        # and model_fields_set contains the field
        if self.icon_url is None and "icon_url" in self.model_fields_set:
            _dict['iconUrl'] = None

        # set to None if on_prem_proxy_url (nullable) is None
        # and model_fields_set contains the field
        if self.on_prem_proxy_url is None and "on_prem_proxy_url" in self.model_fields_set:
            _dict['onPremProxyUrl'] = None

        # set to None if use_on_prem_proxy (nullable) is None
        # and model_fields_set contains the field
        if self.use_on_prem_proxy is None and "use_on_prem_proxy" in self.model_fields_set:
            _dict['useOnPremProxy'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ListAssistants200ResponseInner from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "configResult": ListAssistants200ResponseInnerConfigResult.from_dict(obj["configResult"]) if obj.get("configResult") is not None else None,
            "ownerSlug": obj.get("ownerSlug"),
            "packageSlug": obj.get("packageSlug"),
            "iconUrl": obj.get("iconUrl"),
            "onPremProxyUrl": obj.get("onPremProxyUrl"),
            "useOnPremProxy": obj.get("useOnPremProxy"),
            "rawYaml": obj.get("rawYaml")
        })
        return _obj


