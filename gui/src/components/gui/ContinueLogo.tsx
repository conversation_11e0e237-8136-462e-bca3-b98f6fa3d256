import { vscForeground } from "..";

interface ContinueLogoProps {
  height?: number;
  width?: number;
}

export default function ContinueLogo({
  height = 987,
  width = 299,
}: ContinueLogoProps) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 987 299"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M199.142 100.633L191.304 114.263L211.044 148.774C211.334 149.065 211.334 149.355 211.334 149.645C211.334 149.935 211.334 150.225 211.044 150.515L191.304 185.026L199.142 198.656L227.3 149.645L199.142 100.633ZM188.401 112.523L196.239 98.8928H180.563L172.725 112.523H188.401ZM172.435 116.003L190.723 147.904H206.399L188.111 116.003H172.435ZM188.401 182.996L206.689 151.095H191.014L172.725 182.996H188.401ZM172.435 186.766L180.273 200.396H195.949L188.111 186.766H172.435ZM119.311 203.586C119.021 203.586 118.73 203.586 118.44 203.296C118.15 203.006 117.859 203.006 117.859 202.716L97.8292 168.205H82.1533L110.312 217.217H166.919L159.081 203.586H119.311ZM161.984 201.846L169.822 215.477L177.66 201.846L169.822 188.216L161.984 201.846ZM166.919 186.476H130.052L122.214 200.106H158.791L166.919 186.476ZM127.149 184.736L108.86 152.835L101.022 166.465L119.311 198.366L127.149 184.736ZM82.1533 164.725H97.8292L105.667 151.095H89.9913L82.1533 164.725ZM117.569 96.5728C117.569 96.2827 117.859 95.9927 118.15 95.9927C118.44 95.9927 118.73 95.7027 119.021 95.7027H158.791L166.629 82.0723H110.312L82.1533 131.084H97.8292L117.569 96.5728ZM105.667 148.194L97.8292 134.564H82.1533L89.9913 148.194H105.667ZM119.021 100.923L100.732 132.824L108.57 146.454L126.859 114.553L119.021 100.923ZM159.081 99.1828H122.214L130.052 112.813H166.919L159.081 99.1828ZM169.822 111.073L177.66 97.4428L169.822 83.8123L161.984 97.4428L169.822 111.073Z"
        fill={vscForeground}
      />

      <path
        d="M512.369 123.254C501.918 123.254 493.5 128.184 487.984 136.884H486.533V126.154H473.76V203.587H486.533V158.345C486.533 144.715 497.854 134.854 510.917 134.854C523.981 134.854 532.109 144.425 532.109 157.475V203.877H544.882V157.475C544.592 137.754 532.109 123.254 512.369 123.254Z"
        fill={vscForeground}
      />
      <path
        d="M419.475 122.964C395.09 122.964 377.673 140.364 377.673 164.725C377.673 189.086 394.8 206.777 419.475 206.777C444.15 206.777 461.278 189.376 461.278 164.725C461.278 140.074 443.57 122.964 419.475 122.964ZM419.475 195.176C402.638 195.176 390.446 182.416 390.446 164.725C390.446 147.035 402.348 134.564 419.475 134.564C436.603 134.564 448.214 147.325 448.214 164.725C448.214 182.126 436.022 195.176 419.475 195.176Z"
        fill={vscForeground}
      />
      <path
        d="M354.449 170.235C349.224 184.736 334.128 195.466 317.582 195.466C293.197 195.466 275.489 176.325 275.489 149.645C275.489 122.964 292.907 104.113 317.582 104.113C334.709 104.113 350.095 115.423 354.739 131.084H368.964C363.738 108.463 341.966 91.6426 317.582 91.6426C285.069 91.6426 261.555 116.003 261.555 149.645C261.265 183.286 285.069 207.646 317.582 207.646C341.966 207.646 363.448 191.986 368.673 170.235H354.449Z"
        fill={vscForeground}
      />
      <path
        d="M579.137 183.287V136.305H599.167V126.155H579.137L579.427 101.214H569.557L566.654 126.155H554.171V136.305H566.654V183.867C566.654 197.497 575.073 206.777 588.136 206.777C591.91 206.777 597.135 205.907 600.618 204.747V194.307C597.716 195.177 593.651 195.757 591.329 195.757C583.781 195.757 579.137 190.537 579.137 183.287Z"
        fill={vscForeground}
      />
      <path
        d="M797.438 171.396C797.438 185.026 786.116 194.887 773.053 194.887C759.99 194.887 752.152 185.606 752.152 172.266V126.154H739.379V172.266C739.379 191.987 751.862 206.487 771.602 206.487C781.762 206.487 790.471 201.557 795.986 193.147H797.438V203.877H810.211V126.154H797.438V171.396Z"
        fill={vscForeground}
      />
      <path
        d="M691.19 123.254C680.74 123.254 672.321 128.184 666.805 136.884H665.354V126.154H652.581V203.587H665.354V158.345C665.354 144.715 676.675 134.854 689.739 134.854C702.802 134.854 710.93 144.425 710.93 157.475V203.877H723.703V157.475C723.703 137.754 710.93 123.254 691.19 123.254Z"
        fill={vscForeground}
      />
      <path
        d="M630.809 126.154H618.036V203.587H630.809V126.154Z"
        fill={vscForeground}
      />
      <path
        d="M904.847 164.145C904.847 140.364 887.72 123.254 864.206 123.254C840.692 123.254 822.984 140.364 822.984 164.145C822.984 189.086 840.402 207.067 864.786 207.067C881.914 207.067 896.138 198.946 902.234 185.896L891.784 180.676C886.849 189.956 876.688 195.756 865.077 195.756C849.11 195.756 837.789 185.316 836.047 169.655H904.847C904.847 167.335 904.847 165.595 904.847 164.145ZM836.047 158.055C838.079 144.135 849.11 134.854 863.335 134.854C877.559 134.854 888.881 144.135 891.203 158.055H836.047Z"
        fill={vscForeground}
      />
      <path
        d="M624.423 93.3828C618.907 93.3828 615.133 97.1529 615.133 102.373C615.133 107.593 618.907 111.653 624.423 111.653C629.938 111.653 633.422 107.883 633.422 102.373C633.422 96.8629 629.648 93.3828 624.423 93.3828Z"
        fill={vscForeground}
      />
    </svg>
  );
}
