interface DiscordIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number;
}

export function DiscordIcon({
  size = 24,
  className,
  ...props
}: DiscordIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      className={className}
      {...props}
    >
      <path
        d="M20.317 4.369a19.791 19.791 0 00-4.885-1.515.074.074 0 00-.079.038 13.875 13.875 0 00-.6 1.235 18.337 18.337 0 00-5.487 0 13.872 13.872 0 00-.601-1.235.076.076 0 00-.079-.038A19.736 19.736 0 003.677 4.369a.069.069 0 00-.032.027C.533 9.104-.32 13.726.099 18.306a.082.082 0 00.031.054 19.911 19.911 0 005.993 3.04.078.078 0 00.084-.027 14.345 14.345 0 001.273-2.084.076.076 0 00-.041-.105 13.134 13.134 0 01-1.872-.891.077.077 0 01-.008-.128c.126-.094.252-.192.373-.291a.074.074 0 01.077-.01c3.927 1.794 8.18 1.794 12.061 0a.074.074 0 01.078.009c.121.099.247.198.374.292a.077.077 0 01-.006.127 12.48 12.48 0 01-1.873.891.076.076 0 00-.04.106 14.504 14.504 0 001.273 2.083.077.077 0 00.084.028 19.888 19.888 0 005.993-3.04.077.077 0 00.031-.054c.5-5.177-.838-9.772-3.576-13.91a.061.061 0 00-.032-.028zM9.237 15.335c-1.182 0-2.156-1.085-2.156-2.419 0-1.333.955-2.418 2.156-2.418 1.21 0 2.184 1.085 2.156 2.418.001 1.334-.946 2.419-2.156 2.419zm5.526 0c-1.182 0-2.156-1.085-2.156-2.419 0-1.333.955-2.418 2.156-2.418 1.21 0 2.184 1.085 2.156 2.418 0 1.334-.946 2.419-2.156 2.419z"
        fill="currentColor"
        strokeWidth="0"
      />
    </svg>
  );
}
