package src;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateUtils {

    private static final String DEFAULT_FORMAT = "yyyy-MM-dd";

    /**
     * Convert a Date object to a string representation using the default format (yyyy-MM-dd).
     *
     * @param date the Date object to be converted.
     * @return formatted date string.
     */
    public static String formatDate(Date date) {
        return new SimpleDateFormat(DEFAULT_FORMAT).format(date);
    }

    /**
     * Parse a string representation of a date to a Date object using the default format (yyyy-MM-dd).
     *
     * @param dateString the string representation of the date.
     * @return Date object parsed from the string.
     * @throws ParseException if the dateString cannot be parsed.
     */
    public static Date parseDate(String dateString) throws ParseException {
        return new SimpleDateFormat(DEFAULT_FORMAT).parse(dateString);
    }

    /**
     * Convert a Date object to a string representation using a specified format.
     *
     * @param date   the Date object to be converted.
     * @param format the desired format for the date string.
     * @return formatted date string.
     */
    public static String formatDate(Date date, String format) {
        return new SimpleDateFormat(format).format(date);
    }

    /**
     * Parse a string representation of a date to a Date object using a specified format.
     *
     * @param dateString the string representation of the date.
     * @param format     the desired format for parsing the date string.
     * @return Date object parsed from the string.
     * @throws ParseException if the dateString cannot be parsed.
     */
    public static Date parseDate(String dateString, String format) throws ParseException {
        return new SimpleDateFormat(format).parse(dateString);
    }
}
