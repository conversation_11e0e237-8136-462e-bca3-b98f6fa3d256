/**
 * HelloWorld类 - 展示Java基础功能示例
 * 该类包含几个实用方法以及一个有潜在空指针异常的main方法
 */
import java.net.InetAddress;
import java.net.UnknownHostException;

public class HelloWorld {
    /**
     * 程序入口点
     * 注意：此方法目前包含一个会引发NullPointerException的问题
     *
     * @param args 命令行参数（未使用）
     */
    public static void main(String[] args) {
        // 初始化一个null字符串
        String str = null;

        // 尝试获取null字符串的长度，这会引发NullPointerException
        // 建议在调用length()前添加null检查
        System.out.println(str.length());
    }

    /**
     * Returns the current time as a string.
     *
     * @return The string representation of the current time.
     */
    public static String getCurrentTime() {
        // 获取系统当前时间
        java.time.LocalTime currentTime = java.time.LocalTime.now();
        // 打印数字1用于调试目的
        System.out.println(1);
        // 输出“你好”
        System.out.println("你好");
        // 将readme的内容输出出来
        // 循环5此
        for (int i = 0; i < 5; i++) {
            // 打印数字2用于调试目的
            System.out.println(2);
        }
        // 返回格式化后的时间字符串
        return currentTime.toString();
    }

    /**
     * Retrieves the local IP address of the machine.
     * 获取本机的IP地址
     *
     * @return A string containing the local IP address or an error message.
     */
    public static String getLocalIpAddress() {
        try {
            // 获取本地主机的InetAddress对象
            InetAddress localHost = InetAddress.getLocalHost();
            // 返回包含IP地址的字符串
            return "本地 IP 地址: " + localHost.getHostAddress();
        } catch (UnknownHostException e) {
            // 当无法确定主机地址时返回错误信息
            return "无法确定本地 IP 地址";
        }
    }
}
